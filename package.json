{"name": "collaborative-pixel-art-board", "version": "1.0.0", "description": "A collaborative pixel art drawing board with real-time synchronization", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["pixel-art", "collaborative", "websocket", "canvas"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ws": "^8.14.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}