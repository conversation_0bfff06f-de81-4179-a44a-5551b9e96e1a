<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Art Drawing Board</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header with title and main controls -->
        <header class="header">
            <h1>Pixel Art Drawing Board</h1>
            <div class="main-controls">
                <button id="undoBtn" class="control-btn" title="Undo (Ctrl+Z)">↶</button>
                <button id="redoBtn" class="control-btn" title="Redo (Ctrl+Y)">↷</button>
                <button id="clearBtn" class="control-btn" title="Clear Canvas">🗑️</button>
                <button id="saveBtn" class="control-btn" title="Save as PNG">💾</button>
                <input type="file" id="loadBtn" accept="image/*" style="display: none;">
                <button onclick="document.getElementById('loadBtn').click()" class="control-btn" title="Load Image">📁</button>
            </div>
        </header>

        <!-- Main content area -->
        <div class="main-content">
            <!-- Left sidebar with tools -->
            <aside class="sidebar">
                <!-- Color Palette -->
                <div class="tool-section">
                    <h3>Colors</h3>
                    <div class="color-palette">
                        <div class="current-color-display">
                            <div id="currentColor" class="current-color" style="background-color: #000000;"></div>
                            <span>Current</span>
                        </div>
                        <div class="color-grid">
                            <!-- Predefined colors -->
                            <div class="color-swatch" data-color="#000000" style="background-color: #000000;" title="Black"></div>
                            <div class="color-swatch" data-color="#FFFFFF" style="background-color: #FFFFFF;" title="White"></div>
                            <div class="color-swatch" data-color="#FF0000" style="background-color: #FF0000;" title="Red"></div>
                            <div class="color-swatch" data-color="#00FF00" style="background-color: #00FF00;" title="Green"></div>
                            <div class="color-swatch" data-color="#0000FF" style="background-color: #0000FF;" title="Blue"></div>
                            <div class="color-swatch" data-color="#FFFF00" style="background-color: #FFFF00;" title="Yellow"></div>
                            <div class="color-swatch" data-color="#FF00FF" style="background-color: #FF00FF;" title="Magenta"></div>
                            <div class="color-swatch" data-color="#00FFFF" style="background-color: #00FFFF;" title="Cyan"></div>
                            <div class="color-swatch" data-color="#FFA500" style="background-color: #FFA500;" title="Orange"></div>
                            <div class="color-swatch" data-color="#800080" style="background-color: #800080;" title="Purple"></div>
                            <div class="color-swatch" data-color="#FFC0CB" style="background-color: #FFC0CB;" title="Pink"></div>
                            <div class="color-swatch" data-color="#A52A2A" style="background-color: #A52A2A;" title="Brown"></div>
                            <div class="color-swatch" data-color="#808080" style="background-color: #808080;" title="Gray"></div>
                            <div class="color-swatch" data-color="#90EE90" style="background-color: #90EE90;" title="Light Green"></div>
                            <div class="color-swatch" data-color="#87CEEB" style="background-color: #87CEEB;" title="Sky Blue"></div>
                            <div class="color-swatch" data-color="#DDA0DD" style="background-color: #DDA0DD;" title="Plum"></div>
                        </div>
                        <div class="custom-color-section">
                            <input type="color" id="colorPicker" value="#000000">
                            <label for="colorPicker">Custom</label>
                        </div>
                    </div>
                </div>

                <!-- Brush Tools -->
                <div class="tool-section">
                    <h3>Brush Size</h3>
                    <div class="brush-sizes">
                        <button class="brush-btn active" data-size="1">1x1</button>
                        <button class="brush-btn" data-size="2">2x2</button>
                        <button class="brush-btn" data-size="3">3x3</button>
                        <button class="brush-btn" data-size="5">5x5</button>
                    </div>
                </div>

                <!-- Zoom Controls -->
                <div class="tool-section">
                    <h3>Zoom</h3>
                    <div class="zoom-controls">
                        <button id="zoomOut" class="control-btn">-</button>
                        <span id="zoomLevel">100%</span>
                        <button id="zoomIn" class="control-btn">+</button>
                    </div>
                    <div class="zoom-presets">
                        <button class="zoom-preset" data-zoom="0.25">25%</button>
                        <button class="zoom-preset" data-zoom="0.5">50%</button>
                        <button class="zoom-preset active" data-zoom="1">100%</button>
                        <button class="zoom-preset" data-zoom="2">200%</button>
                        <button class="zoom-preset" data-zoom="4">400%</button>
                    </div>
                </div>

                <!-- Tools -->
                <div class="tool-section">
                    <h3>Tools</h3>
                    <div class="tools">
                        <button id="drawTool" class="tool-btn active" title="Draw (D)">✏️</button>
                        <button id="eraseTool" class="tool-btn" title="Erase (E)">🧽</button>
                        <button id="panTool" class="tool-btn" title="Pan (Space)">✋</button>
                        <button id="eyedropperTool" class="tool-btn" title="Eyedropper (I)">💧</button>
                    </div>
                </div>

                <!-- Grid Toggle -->
                <div class="tool-section">
                    <label class="checkbox-label">
                        <input type="checkbox" id="gridToggle" checked>
                        <span>Show Grid</span>
                    </label>
                </div>
            </aside>

            <!-- Canvas area -->
            <main class="canvas-area">
                <div class="canvas-container" id="canvasContainer">
                    <canvas id="pixelCanvas" width="1000" height="900"></canvas>
                    <canvas id="gridCanvas" width="1000" height="900"></canvas>
                    <div class="canvas-info">
                        <span id="canvasCoords">X: 0, Y: 0</span>
                        <span id="canvasSize">1000 × 900</span>
                    </div>
                </div>
            </main>
        </div>

        <!-- Status bar -->
        <footer class="status-bar">
            <span id="statusText">Ready to draw</span>
            <span id="actionCount">Actions: 0</span>
        </footer>
    </div>

    <!-- Keyboard shortcuts help -->
    <div id="helpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Keyboard Shortcuts</h2>
            <ul>
                <li><kbd>D</kbd> - Draw tool</li>
                <li><kbd>E</kbd> - Erase tool</li>
                <li><kbd>Space</kbd> - Pan tool (hold)</li>
                <li><kbd>I</kbd> - Eyedropper tool</li>
                <li><kbd>+</kbd> / <kbd>=</kbd> - Zoom in</li>
                <li><kbd>-</kbd> - Zoom out</li>
                <li><kbd>Ctrl+Z</kbd> - Undo</li>
                <li><kbd>Ctrl+Y</kbd> - Redo</li>
                <li><kbd>Ctrl+S</kbd> - Save</li>
                <li><kbd>G</kbd> - Toggle grid</li>
                <li><kbd>H</kbd> - Show this help</li>
            </ul>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
