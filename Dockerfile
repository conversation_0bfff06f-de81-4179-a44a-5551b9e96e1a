# Minimal production Dockerfile for the collaborative pixel art board
# No extra deploy scripts required

FROM node:18-alpine

# Create app directory
WORKDIR /usr/src/app

# Install dependencies
# Copy only package files first to leverage Docker layer caching
COPY package*.json ./

# Install only production dependencies
# Use npm install (not ci) to work even without a lockfile
RUN npm install --production

# Bundle app source
COPY . .

# Expose the app port
EXPOSE 3000

# Start the server
CMD ["npm", "start"]

