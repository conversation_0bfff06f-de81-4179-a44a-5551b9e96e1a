# Pixel Art Drawing Board

A modern, feature-rich pixel art drawing application built with HTML5, CSS3, and JavaScript. Create detailed pixel art with an intuitive interface similar to Reddit's r/place collaborative pixel art project.

## Features

### Canvas
- **1000×900 pixel canvas** - Large drawing area for detailed artwork
- **Individual pixel control** - Each pixel is clickable and paintable
- **Grid overlay** - Toggle-able grid to clearly show pixel boundaries
- **High-performance rendering** - Optimized for smooth drawing experience

### Drawing Tools
- **Multiple brush sizes** - 1×1, 2×2, 3×3, and 5×5 pixel brushes
- **Color palette** - 16 predefined colors plus custom color picker
- **Drawing modes**:
  - ✏️ **Draw** - Paint pixels with selected color
  - 🧽 **Erase** - Remove pixels (right-click also erases)
  - ✋ **Pan** - Navigate around the canvas when zoomed
  - 💧 **Eyedropper** - Pick colors from existing pixels

### Navigation & Zoom
- **Smooth zoom** - 5 preset levels: 25%, 50%, 100%, 200%, 400%
- **Pan/drag** - Navigate around canvas when zoomed in
- **Mouse wheel zoom** - Scroll to zoom in/out
- **Coordinate display** - Shows current mouse position

### History & Actions
- **Undo/Redo** - Full action history with 50-step memory
- **Clear canvas** - Reset entire drawing area
- **Action counter** - Track number of drawing actions

### File Operations
- **Save as PNG** - Export artwork as high-quality PNG image
- **Load image** - Import existing images to trace over
- **Automatic naming** - Saved files include timestamp

### User Interface
- **Responsive design** - Works on desktop and mobile devices
- **Clean interface** - Intuitive layout inspired by professional art tools
- **Status indicators** - Real-time feedback on current tool and zoom level
- **Keyboard shortcuts** - Quick access to common functions

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| `D` | Switch to Draw tool |
| `E` | Switch to Erase tool |
| `I` | Switch to Eyedropper tool |
| `Space` | Hold for Pan tool |
| `+` / `=` | Zoom in |
| `-` | Zoom out |
| `G` | Toggle grid visibility |
| `H` | Show help modal |
| `Ctrl+Z` | Undo |
| `Ctrl+Y` | Redo |
| `Ctrl+S` | Save image |

## Usage

### Getting Started
1. Open `index.html` in a modern web browser
2. Select a color from the palette or use the custom color picker
3. Choose your brush size (1×1 for detailed work, larger for filling areas)
4. Start drawing by clicking on the canvas

### Drawing Techniques
- **Single pixels**: Use 1×1 brush for detailed work
- **Filling areas**: Use larger brushes (2×2, 3×3, 5×5)
- **Erasing**: Right-click to erase, or switch to Erase tool
- **Color picking**: Use Eyedropper tool to sample colors from your artwork

### Navigation
- **Zoom in** for detailed pixel work
- **Pan around** when zoomed in by using the Pan tool or holding Space
- **Use the grid** (visible at high zoom levels) to align pixels perfectly

### Saving Your Work
- Click the 💾 Save button to download your artwork as PNG
- Files are automatically named with timestamp
- Load existing images using the 📁 Load button to trace or modify

## Technical Details

### Performance Optimizations
- **Efficient pixel storage** - 2D array structure for fast access
- **Smart redrawing** - Only updates changed pixels
- **Grid rendering** - Conditional grid display based on zoom level
- **Memory management** - Limited undo history to prevent memory issues

### Browser Compatibility
- **Modern browsers** - Chrome, Firefox, Safari, Edge (latest versions)
- **HTML5 Canvas** - Hardware-accelerated rendering
- **Touch support** - Works on tablets and touch devices
- **Responsive design** - Adapts to different screen sizes

### File Structure
```
├── index.html          # Main HTML structure
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript application logic
└── README.md           # This documentation
```

## Browser Support

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Tips for Best Results

1. **Start with larger brushes** for base shapes, then use 1×1 for details
2. **Use the grid** when working on precise pixel alignment
3. **Zoom in** for detailed work - the interface is optimized for high zoom levels
4. **Save frequently** - Use Ctrl+S to quickly save your progress
5. **Experiment with colors** - Use the eyedropper to create color harmony
6. **Plan your composition** - The 1000×900 canvas gives you plenty of room

## License

This project is open source and available under the MIT License.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the application.

---

**Happy pixel art creation!** 🎨
