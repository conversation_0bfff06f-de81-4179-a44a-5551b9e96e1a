const express = require('express');
const fs = require('fs');
const path = require('path');
const http = require('http');
const cors = require('cors');
const WebSocket = require('ws');

const PORT = process.env.PORT || 3000;
const DATA_DIR = path.join(__dirname, 'data');
const DATA_FILE = path.join(DATA_DIR, 'board.json');

const WIDTH = 1000;
const HEIGHT = 900;

// Ensure data directory exists
if (!fs.existsSync(DATA_DIR)) {
  fs.mkdirSync(DATA_DIR, { recursive: true });
}

// Load or initialize board state (sparse map: key "x,y" -> color)
let board = { width: WIDTH, height: HEIGHT, pixels: {} };
try {
  if (fs.existsSync(DATA_FILE)) {
    const content = fs.readFileSync(DATA_FILE, 'utf8');
    const parsed = JSON.parse(content);
    if (parsed && parsed.pixels) {
      board = { width: WIDTH, height: HEIGHT, pixels: parsed.pixels };
    }
  }
} catch (e) {
  console.error('Failed to load board file, starting fresh:', e);
}

const app = express();
app.use(cors()); // allow CORS for all origins (adjust for prod)
app.use(express.json({ limit: '10mb' }));

// Serve static files (front-end)
app.use(express.static(__dirname));

// REST: fetch current board
app.get('/api/board', (req, res) => {
  res.json({ width: WIDTH, height: HEIGHT, pixels: board.pixels });
});

// REST: overwrite board (optional admin)
app.post('/api/board', (req, res) => {
  const { pixels } = req.body || {};
  if (!pixels || typeof pixels !== 'object') {
    return res.status(400).json({ error: 'invalid pixels' });
  }
  board.pixels = pixels;
  schedulePersist();
  broadcast({ type: 'full', pixels });
  res.json({ ok: true });
});

const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

// Broadcast helper
function broadcast(msg, exclude) {
  const data = JSON.stringify(msg);
  wss.clients.forEach((client) => {
    if (client !== exclude && client.readyState === WebSocket.OPEN) {
      client.send(data);
    }
  });
}

// Persist with throttle
let persistTimer = null;
function schedulePersist() {
  if (persistTimer) return;
  persistTimer = setTimeout(() => {
    persistTimer = null;
    try {
      fs.writeFileSync(DATA_FILE, JSON.stringify({ pixels: board.pixels }));
    } catch (e) {
      console.error('Failed to persist board:', e);
    }
  }, 1000); // persist at most once per second
}

wss.on('connection', (ws) => {
  ws.on('message', (raw) => {
    try {
      const msg = JSON.parse(raw);
      if (msg.type === 'pixels' && Array.isArray(msg.ops)) {
        for (const op of msg.ops) {
          const { x, y, color } = op;
          if (
            Number.isInteger(x) && Number.isInteger(y) &&
            x >= 0 && x < WIDTH && y >= 0 && y < HEIGHT &&
            typeof color === 'string'
          ) {
            const key = `${x},${y}`;
            if (color === 'transparent') {
              delete board.pixels[key];
            } else {
              board.pixels[key] = color;
            }
          }
        }
        schedulePersist();
        broadcast({ type: 'pixels', ops: msg.ops }, ws);
      }
    } catch (e) {
      console.error('Invalid ws message:', e);
    }
  });
});

server.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
