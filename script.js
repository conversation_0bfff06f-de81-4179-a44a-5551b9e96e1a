class PixelArtBoard {
    constructor() {
        this.canvas = document.getElementById('pixelCanvas');
        this.gridCanvas = document.getElementById('gridCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridCtx = this.gridCanvas.getContext('2d');
        
        // Canvas properties
        this.canvasWidth = 1000;
        this.canvasHeight = 900;
        this.pixelSize = 1; // Each pixel is 1x1 on the canvas
        
        // State
        this.currentColor = '#000000';
        this.currentTool = 'draw';
        this.brushSize = 1;
        this.zoom = 1;
        this.panX = 0;
        this.panY = 0;
        this.isDrawing = false;
        this.showGrid = true;
        
        // Pixel data - 2D array to store pixel colors
        this.pixelData = Array(this.canvasHeight).fill().map(() => 
            Array(this.canvasWidth).fill('transparent')
        );
        
        // History for undo/redo
        this.history = [];
        this.historyStep = -1;
        this.maxHistorySteps = 50;
        
        // Mouse state
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        this.mouseDown = false;
        
        this.init();
    }
    
    init() {
        this.setupCanvas();
        this.setupEventListeners();
        this.drawGrid();
        this.saveState(); // Initial state
        this.setTool('draw'); // Ensure draw tool is active
        this.updateUI();
    }
    
    setupCanvas() {
        // Set canvas size
        this.canvas.width = this.canvasWidth;
        this.canvas.height = this.canvasHeight;
        this.gridCanvas.width = this.canvasWidth;
        this.gridCanvas.height = this.canvasHeight;
        
        // Disable image smoothing for crisp pixels
        this.ctx.imageSmoothingEnabled = false;
        this.gridCtx.imageSmoothingEnabled = false;
        
        // Set initial transform
        this.updateCanvasTransform();
    }
    
    setupEventListeners() {
        // Canvas mouse events
        this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this));
        this.canvas.addEventListener('contextmenu', this.handleRightClick.bind(this));
        this.canvas.addEventListener('wheel', this.handleWheel.bind(this));
        
        // Touch events for mobile
        this.canvas.addEventListener('touchstart', this.handleTouchStart.bind(this));
        this.canvas.addEventListener('touchmove', this.handleTouchMove.bind(this));
        this.canvas.addEventListener('touchend', this.handleTouchEnd.bind(this));
        
        // Color palette
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.addEventListener('click', () => {
                this.setColor(swatch.dataset.color);
            });
        });
        
        document.getElementById('colorPicker').addEventListener('change', (e) => {
            this.setColor(e.target.value);
        });
        
        // Brush sizes
        document.querySelectorAll('.brush-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.setBrushSize(parseInt(btn.dataset.size));
            });
        });
        
        // Tools - only draw tool needed
        document.getElementById('drawTool').addEventListener('click', () => this.setTool('draw'));
        
        // Zoom controls
        document.getElementById('zoomIn').addEventListener('click', () => this.zoomIn());
        document.getElementById('zoomOut').addEventListener('click', () => this.zoomOut());
        
        document.querySelectorAll('.zoom-preset').forEach(btn => {
            btn.addEventListener('click', () => {
                this.setZoom(parseFloat(btn.dataset.zoom));
            });
        });
        
        // Main controls
        document.getElementById('undoBtn').addEventListener('click', () => this.undo());
        document.getElementById('redoBtn').addEventListener('click', () => this.redo());
        document.getElementById('clearBtn').addEventListener('click', () => this.clearCanvas());
        document.getElementById('saveBtn').addEventListener('click', () => this.saveImage());
        document.getElementById('loadBtn').addEventListener('change', this.loadImage.bind(this));
        
        // Grid toggle
        document.getElementById('gridToggle').addEventListener('change', (e) => {
            this.showGrid = e.target.checked;
            this.toggleGrid();
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
        document.addEventListener('keyup', this.handleKeyUp.bind(this));
        
        // Prevent context menu on canvas
        this.canvas.addEventListener('contextmenu', e => e.preventDefault());
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    getCanvasCoordinates(clientX, clientY) {
        const rect = this.canvas.getBoundingClientRect();

        // Calculate the actual canvas position considering zoom and pan
        // The transform is: translate(panX, panY) scale(zoom)
        // So to reverse: (point - pan) / zoom
        const canvasX = (clientX - rect.left - this.panX) / this.zoom;
        const canvasY = (clientY - rect.top - this.panY) / this.zoom;

        // Convert to pixel coordinates
        const x = Math.floor(canvasX);
        const y = Math.floor(canvasY);

        return { x, y };
    }
    
    handleMouseDown(e) {
        e.preventDefault();
        this.mouseDown = true;
        this.isDrawing = true;

        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.lastMouseX = coords.x;
        this.lastMouseY = coords.y;

        // Always draw when mouse is pressed
        this.drawPixel(coords.x, coords.y);
    }
    
    handleMouseMove(e) {
        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.updateCoordinateDisplay(coords.x, coords.y);

        if (!this.mouseDown) return;

        // Continue drawing when mouse is dragged
        if (this.isDrawing) {
            this.drawPixel(coords.x, coords.y);
        }

        this.lastMouseX = coords.x;
        this.lastMouseY = coords.y;
    }
    
    handleMouseUp() {
        if (this.isDrawing) {
            this.saveState();
        }

        this.mouseDown = false;
        this.isDrawing = false;
    }
    
    handleRightClick(e) {
        e.preventDefault();
        const coords = this.getCanvasCoordinates(e.clientX, e.clientY);
        this.erasePixel(coords.x, coords.y);
        this.saveState();
    }
    
    handleWheel(e) {
        e.preventDefault();
        const delta = e.deltaY > 0 ? -0.1 : 0.1;
        const newZoom = Math.max(0.1, Math.min(10, this.zoom + delta));
        this.setZoom(newZoom);
    }
    
    drawPixel(x, y) {
        if (x < 0 || x >= this.canvasWidth || y < 0 || y >= this.canvasHeight) return;

        // Always use current color for drawing
        const color = this.currentColor;

        // Draw with brush size
        const halfBrush = Math.floor(this.brushSize / 2);
        for (let dy = -halfBrush; dy <= halfBrush; dy++) {
            for (let dx = -halfBrush; dx <= halfBrush; dx++) {
                const px = x + dx;
                const py = y + dy;

                if (px >= 0 && px < this.canvasWidth && py >= 0 && py < this.canvasHeight) {
                    this.setPixel(px, py, color);
                }
            }
        }
    }
    
    erasePixel(x, y) {
        if (x < 0 || x >= this.canvasWidth || y < 0 || y >= this.canvasHeight) return;
        
        const halfBrush = Math.floor(this.brushSize / 2);
        for (let dy = -halfBrush; dy <= halfBrush; dy++) {
            for (let dx = -halfBrush; dx <= halfBrush; dx++) {
                const px = x + dx;
                const py = y + dy;
                
                if (px >= 0 && px < this.canvasWidth && py >= 0 && py < this.canvasHeight) {
                    this.setPixel(px, py, 'transparent');
                }
            }
        }
    }
    
    setPixel(x, y, color) {
        // Only update if color actually changed
        if (this.pixelData[y][x] === color) return;

        this.pixelData[y][x] = color;

        // Draw on canvas
        if (color === 'transparent') {
            this.ctx.clearRect(x, y, 1, 1);
        } else {
            this.ctx.fillStyle = color;
            this.ctx.fillRect(x, y, 1, 1);
        }
    }
    

    
    setColor(color) {
        this.currentColor = color;
        document.getElementById('currentColor').style.backgroundColor = color;
        document.getElementById('colorPicker').value = color;
        
        // Update active color swatch
        document.querySelectorAll('.color-swatch').forEach(swatch => {
            swatch.classList.toggle('active', swatch.dataset.color === color);
        });
    }
    
    setTool(tool) {
        this.currentTool = tool;

        // Update tool buttons
        document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
        document.getElementById('drawTool').classList.add('active');

        // Always use draw cursor
        const container = document.getElementById('canvasContainer');
        container.className = 'canvas-container draw-cursor';
    }
    
    setBrushSize(size) {
        this.brushSize = size;
        
        // Update brush buttons
        document.querySelectorAll('.brush-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-size="${size}"]`).classList.add('active');
    }
    
    setZoom(zoom) {
        this.zoom = Math.max(0.1, Math.min(10, zoom));
        this.updateCanvasTransform();
        this.updateUI();
    }
    
    zoomIn() {
        const zoomLevels = [0.25, 0.5, 1, 2, 4];
        const currentIndex = zoomLevels.findIndex(level => level >= this.zoom);
        const nextIndex = Math.min(currentIndex + 1, zoomLevels.length - 1);
        this.setZoom(zoomLevels[nextIndex]);
    }
    
    zoomOut() {
        const zoomLevels = [0.25, 0.5, 1, 2, 4];
        const currentIndex = zoomLevels.findIndex(level => level >= this.zoom);
        const prevIndex = Math.max(currentIndex - 1, 0);
        this.setZoom(zoomLevels[prevIndex]);
    }
    
    updateCanvasTransform() {
        const transform = `translate(${this.panX}px, ${this.panY}px) scale(${this.zoom})`;
        this.canvas.style.transform = transform;
        this.gridCanvas.style.transform = transform;
        this.canvas.style.transformOrigin = '0 0';
        this.gridCanvas.style.transformOrigin = '0 0';
    }
    
    drawGrid() {
        if (!this.showGrid) return;
        
        this.gridCtx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
        this.gridCtx.strokeStyle = '#ddd';
        this.gridCtx.lineWidth = 0.5;
        
        // Only draw grid when zoomed in enough to see individual pixels
        if (this.zoom < 4) return;
        
        // Vertical lines
        for (let x = 0; x <= this.canvasWidth; x += this.pixelSize) {
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(x, 0);
            this.gridCtx.lineTo(x, this.canvasHeight);
            this.gridCtx.stroke();
        }
        
        // Horizontal lines
        for (let y = 0; y <= this.canvasHeight; y += this.pixelSize) {
            this.gridCtx.beginPath();
            this.gridCtx.moveTo(0, y);
            this.gridCtx.lineTo(this.canvasWidth, y);
            this.gridCtx.stroke();
        }
    }
    
    toggleGrid() {
        this.gridCanvas.style.display = this.showGrid ? 'block' : 'none';
        this.drawGrid();
    }

    saveState() {
        // Remove any future history if we're not at the end
        this.history = this.history.slice(0, this.historyStep + 1);

        // Add current state
        const state = this.pixelData.map(row => [...row]);
        this.history.push(state);

        // Limit history size
        if (this.history.length > this.maxHistorySteps) {
            this.history.shift();
        } else {
            this.historyStep++;
        }

        this.updateUI();
    }

    undo() {
        if (this.historyStep > 0) {
            this.historyStep--;
            this.loadState(this.history[this.historyStep]);
            this.updateUI();
        }
    }

    redo() {
        if (this.historyStep < this.history.length - 1) {
            this.historyStep++;
            this.loadState(this.history[this.historyStep]);
            this.updateUI();
        }
    }

    loadState(state) {
        this.pixelData = state.map(row => [...row]);
        this.redrawCanvas();
    }

    redrawCanvas() {
        this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);

        for (let y = 0; y < this.canvasHeight; y++) {
            for (let x = 0; x < this.canvasWidth; x++) {
                const color = this.pixelData[y][x];
                if (color !== 'transparent') {
                    this.ctx.fillStyle = color;
                    this.ctx.fillRect(x, y, 1, 1);
                }
            }
        }
    }

    clearCanvas() {
        if (confirm('Are you sure you want to clear the canvas?')) {
            this.pixelData = Array(this.canvasHeight).fill().map(() =>
                Array(this.canvasWidth).fill('transparent')
            );
            this.ctx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
            this.saveState();
        }
    }

    saveImage() {
        // Create a temporary canvas with the actual pixel data
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = this.canvasWidth;
        tempCanvas.height = this.canvasHeight;
        const tempCtx = tempCanvas.getContext('2d');

        // Draw the pixel data
        for (let y = 0; y < this.canvasHeight; y++) {
            for (let x = 0; x < this.canvasWidth; x++) {
                const color = this.pixelData[y][x];
                if (color !== 'transparent') {
                    tempCtx.fillStyle = color;
                    tempCtx.fillRect(x, y, 1, 1);
                }
            }
        }

        // Download the image
        const link = document.createElement('a');
        link.download = `pixel-art-${Date.now()}.png`;
        link.href = tempCanvas.toDataURL();
        link.click();
    }

    loadImage(e) {
        const file = e.target.files[0];
        if (!file) return;

        const img = new Image();
        img.onload = () => {
            // Create a temporary canvas to read pixel data
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = Math.min(img.width, this.canvasWidth);
            tempCanvas.height = Math.min(img.height, this.canvasHeight);
            const tempCtx = tempCanvas.getContext('2d');

            // Draw the image
            tempCtx.drawImage(img, 0, 0, tempCanvas.width, tempCanvas.height);

            // Read pixel data
            const imageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
            const data = imageData.data;

            // Clear current canvas
            this.pixelData = Array(this.canvasHeight).fill().map(() =>
                Array(this.canvasWidth).fill('transparent')
            );

            // Set pixel data from image
            for (let y = 0; y < tempCanvas.height; y++) {
                for (let x = 0; x < tempCanvas.width; x++) {
                    const index = (y * tempCanvas.width + x) * 4;
                    const r = data[index];
                    const g = data[index + 1];
                    const b = data[index + 2];
                    const a = data[index + 3];

                    if (a > 128) { // Only set opaque pixels
                        const color = `rgb(${r}, ${g}, ${b})`;
                        this.pixelData[y][x] = color;
                    }
                }
            }

            this.redrawCanvas();
            this.saveState();
        };

        img.src = URL.createObjectURL(file);
    }

    handleKeyDown(e) {
        // Prevent default for our shortcuts
        const shortcuts = ['KeyG', 'KeyH', 'Equal', 'Minus'];
        if (shortcuts.includes(e.code) || (e.ctrlKey && ['KeyZ', 'KeyY', 'KeyS'].includes(e.code))) {
            e.preventDefault();
        }

        switch (e.code) {
            case 'KeyG':
                document.getElementById('gridToggle').click();
                break;
            case 'KeyH':
                this.showHelp();
                break;
            case 'Equal':
            case 'NumpadAdd':
                this.zoomIn();
                break;
            case 'Minus':
            case 'NumpadSubtract':
                this.zoomOut();
                break;
        }

        if (e.ctrlKey) {
            switch (e.code) {
                case 'KeyZ':
                    this.undo();
                    break;
                case 'KeyY':
                    this.redo();
                    break;
                case 'KeyS':
                    this.saveImage();
                    break;
            }
        }
    }

    handleKeyUp() {
        // No special key up handling needed for simplified tool set
    }

    showHelp() {
        document.getElementById('helpModal').style.display = 'flex';
    }

    updateCoordinateDisplay(x, y) {
        document.getElementById('canvasCoords').textContent = `X: ${x}, Y: ${y}`;
    }

    updateUI() {
        // Update zoom display
        document.getElementById('zoomLevel').textContent = Math.round(this.zoom * 100) + '%';

        // Update zoom preset buttons
        document.querySelectorAll('.zoom-preset').forEach(btn => {
            btn.classList.toggle('active', parseFloat(btn.dataset.zoom) === this.zoom);
        });

        // Update undo/redo buttons
        document.getElementById('undoBtn').disabled = this.historyStep <= 0;
        document.getElementById('redoBtn').disabled = this.historyStep >= this.history.length - 1;

        // Update action count
        document.getElementById('actionCount').textContent = `Actions: ${this.historyStep}`;

        // Update grid
        this.drawGrid();
    }

    handleResize() {
        // Recalculate canvas positioning on window resize
        this.updateCanvasTransform();
    }

    // Touch event handlers for mobile support
    handleTouchStart(e) {
        e.preventDefault();
        const touch = e.touches[0];
        this.handleMouseDown({ clientX: touch.clientX, clientY: touch.clientY, preventDefault: () => {} });
    }

    handleTouchMove(e) {
        e.preventDefault();
        const touch = e.touches[0];
        this.handleMouseMove({ clientX: touch.clientX, clientY: touch.clientY });
    }

    handleTouchEnd(e) {
        e.preventDefault();
        this.handleMouseUp();
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new PixelArtBoard();

    // Close help modal
    document.querySelector('.close').addEventListener('click', () => {
        document.getElementById('helpModal').style.display = 'none';
    });

    // Close modal when clicking outside
    document.getElementById('helpModal').addEventListener('click', (e) => {
        if (e.target === document.getElementById('helpModal')) {
            document.getElementById('helpModal').style.display = 'none';
        }
    });

    // Status updates
    document.getElementById('statusText').textContent = 'Pixel Art Board Ready - Start drawing!';
});
