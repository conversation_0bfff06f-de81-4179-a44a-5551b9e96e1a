<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pixel Art Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin-top: 10px;
        }
        .test-link:hover {
            background: #5a6fd8;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .feature {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .feature p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="test-info">
        <h1>🎨 像素艺术绘图板 - 测试页面</h1>
        <p>这是一个简化版的像素艺术绘图板，专注于核心绘画功能。</p>
        <a href="index.html" class="test-link">打开绘图板</a>
        
        <div class="features">
            <div class="feature">
                <h3>✏️ 简化工具</h3>
                <p>只保留画笔工具，专注于绘画体验</p>
            </div>
            <div class="feature">
                <h3>🎨 颜色选择</h3>
                <p>16种预设颜色 + 自定义颜色选择器</p>
            </div>
            <div class="feature">
                <h3>🔍 缩放功能</h3>
                <p>5个缩放级别：25%, 50%, 100%, 200%, 400%</p>
            </div>
            <div class="feature">
                <h3>🖱️ 右键擦除</h3>
                <p>右键点击可以擦除像素</p>
            </div>
            <div class="feature">
                <h3>↶ 撤销重做</h3>
                <p>支持50步操作历史记录</p>
            </div>
            <div class="feature">
                <h3>💾 保存导出</h3>
                <p>保存为PNG格式，支持加载图片</p>
            </div>
        </div>
        
        <h2>使用说明</h2>
        <ul>
            <li><strong>绘画：</strong>左键点击或拖拽在画布上绘制</li>
            <li><strong>擦除：</strong>右键点击擦除像素</li>
            <li><strong>缩放：</strong>使用缩放按钮或 +/- 键</li>
            <li><strong>颜色：</strong>点击色板选择颜色或使用自定义颜色选择器</li>
            <li><strong>画笔：</strong>选择不同的画笔大小 (1x1, 2x2, 3x3, 5x5)</li>
            <li><strong>网格：</strong>在高缩放级别下显示像素网格</li>
        </ul>
        
        <h2>键盘快捷键</h2>
        <ul>
            <li><kbd>+</kbd> / <kbd>=</kbd> - 放大</li>
            <li><kbd>-</kbd> - 缩小</li>
            <li><kbd>Ctrl+Z</kbd> - 撤销</li>
            <li><kbd>Ctrl+Y</kbd> - 重做</li>
            <li><kbd>Ctrl+S</kbd> - 保存</li>
            <li><kbd>G</kbd> - 切换网格显示</li>
            <li><kbd>H</kbd> - 显示帮助</li>
        </ul>
        
        <h2>修复内容</h2>
        <ul>
            <li>✅ 简化工具栏，只保留画笔工具</li>
            <li>✅ 修复缩放后坐标计算错误的问题</li>
            <li>✅ 修复颜色切换后无法上色的问题</li>
            <li>✅ 优化变换矩阵计算</li>
            <li>✅ 移除不必要的工具切换逻辑</li>
        </ul>
    </div>
</body>
</html>
