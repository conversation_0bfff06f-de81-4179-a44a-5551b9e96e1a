/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f0f0f0;
    color: #333;
    overflow: hidden;
}

/* App container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 100;
}

.header h1 {
    font-size: 1.5rem;
    font-weight: 600;
}

.main-controls {
    display: flex;
    gap: 8px;
}

.control-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.control-btn:active {
    transform: translateY(0);
}

/* Main content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 250px;
    background: white;
    border-right: 1px solid #ddd;
    padding: 20px;
    overflow-y: auto;
    box-shadow: 2px 0 10px rgba(0,0,0,0.05);
}

.tool-section {
    margin-bottom: 25px;
}

.tool-section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #555;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Color palette */
.color-palette {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.current-color-display {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
}

.current-color {
    width: 40px;
    height: 40px;
    border: 2px solid #333;
    border-radius: 6px;
    cursor: pointer;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 6px;
}

.color-swatch {
    width: 35px;
    height: 35px;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.color-swatch:hover {
    transform: scale(1.1);
    border-color: #333;
}

.color-swatch.active {
    border-color: #667eea;
    border-width: 3px;
}

.custom-color-section {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
}

#colorPicker {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
}

/* Brush sizes */
.brush-sizes {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.brush-btn {
    padding: 8px 12px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.brush-btn:hover {
    border-color: #667eea;
}

.brush-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Zoom controls */
.zoom-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

#zoomLevel {
    font-weight: 600;
    min-width: 50px;
    text-align: center;
}

.zoom-presets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 6px;
}

.zoom-preset {
    padding: 6px 8px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.2s ease;
}

.zoom-preset:hover {
    border-color: #667eea;
}

.zoom-preset.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Tools */
.tools {
    display: flex;
    gap: 8px;
}

.tool-btn {
    padding: 12px;
    border: 2px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.tool-btn:hover {
    border-color: #667eea;
}

.tool-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Checkbox */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Canvas area */
.canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #e8e8e8;
    position: relative;
}

.canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: crosshair;
}

#pixelCanvas, #gridCanvas {
    position: absolute;
    top: 0;
    left: 0;
    background: white;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

#gridCanvas {
    pointer-events: none;
    opacity: 0.3;
}

.canvas-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

/* Status bar */
.status-bar {
    background: #333;
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

/* Modal */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 400px;
    width: 90%;
    position: relative;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
}

.modal-content h2 {
    margin-bottom: 20px;
    color: #333;
}

.modal-content ul {
    list-style: none;
}

.modal-content li {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

kbd {
    background: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 2px 6px;
    font-family: monospace;
    font-size: 11px;
    min-width: 20px;
    text-align: center;
}

/* Responsive design */
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        max-height: 200px;
        padding: 15px;
    }
    
    .tool-section {
        margin-bottom: 15px;
    }
    
    .color-grid {
        grid-template-columns: repeat(8, 1fr);
    }
    
    .header h1 {
        font-size: 1.2rem;
    }
    
    .main-controls {
        gap: 4px;
    }
    
    .control-btn {
        padding: 6px 8px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        max-height: 150px;
        padding: 10px;
    }
    
    .color-grid {
        grid-template-columns: repeat(6, 1fr);
        gap: 4px;
    }
    
    .color-swatch {
        width: 25px;
        height: 25px;
    }
    
    .current-color {
        width: 30px;
        height: 30px;
    }
}

/* Cursor styles for different tools */
.canvas-container.draw-cursor { cursor: crosshair; }
.canvas-container.erase-cursor { cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="none" stroke="red" stroke-width="2"/><line x1="6" y1="6" x2="14" y2="14" stroke="red" stroke-width="2"/></svg>') 10 10, auto; }
.canvas-container.pan-cursor { cursor: grab; }
.canvas-container.pan-cursor:active { cursor: grabbing; }
.canvas-container.eyedropper-cursor { cursor: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="8" fill="none" stroke="blue" stroke-width="2"/><circle cx="10" cy="10" r="2" fill="blue"/></svg>') 10 10, auto; }
